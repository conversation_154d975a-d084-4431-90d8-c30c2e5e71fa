// Code generated by templ - DO NOT EDIT.

// templ: version: v0.3.924
package components

//lint:file-ignore SA4006 This context is only used if a nested component is present.

import "github.com/a-h/templ"
import templruntime "github.com/a-h/templ/runtime"

import (
	"server-panel/internal/environment"
	"server-panel/internal/i18n"
	"strconv"
)

func EnvironmentCard(service environment.Service, currentLang string) templ.Component {
	return templruntime.GeneratedTemplate(func(templ_7745c5c3_Input templruntime.GeneratedComponentInput) (templ_7745c5c3_Err error) {
		templ_7745c5c3_W, ctx := templ_7745c5c3_Input.Writer, templ_7745c5c3_Input.Context
		if templ_7745c5c3_CtxErr := ctx.Err(); templ_7745c5c3_CtxErr != nil {
			return templ_7745c5c3_CtxErr
		}
		templ_7745c5c3_Buffer, templ_7745c5c3_IsBuffer := templruntime.GetBuffer(templ_7745c5c3_W)
		if !templ_7745c5c3_IsBuffer {
			defer func() {
				templ_7745c5c3_BufErr := templruntime.ReleaseBuffer(templ_7745c5c3_Buffer)
				if templ_7745c5c3_Err == nil {
					templ_7745c5c3_Err = templ_7745c5c3_BufErr
				}
			}()
		}
		ctx = templ.InitializeContext(ctx)
		templ_7745c5c3_Var1 := templ.GetChildren(ctx)
		if templ_7745c5c3_Var1 == nil {
			templ_7745c5c3_Var1 = templ.NopComponent
		}
		ctx = templ.ClearChildren(ctx)
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 1, "<div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow h-full flex flex-col\"><div class=\"flex items-start justify-between mb-4\"><div class=\"flex items-center flex-1\"><div class=\"text-2xl mr-3 flex-shrink-0\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var2 string
		templ_7745c5c3_Var2, templ_7745c5c3_Err = templ.JoinStringErrs(service.Icon)
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/environment_card.templ`, Line: 13, Col: 59}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var2))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 2, "</div><div class=\"min-w-0 flex-1\"><h3 class=\"text-lg font-medium text-gray-900 dark:text-gray-100 truncate\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var3 string
		templ_7745c5c3_Var3, templ_7745c5c3_Err = templ.JoinStringErrs(service.DisplayName)
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/environment_card.templ`, Line: 15, Col: 100}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var3))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 3, "</h3><p class=\"text-sm text-gray-500 dark:text-gray-400 line-clamp-2\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		var templ_7745c5c3_Var4 string
		templ_7745c5c3_Var4, templ_7745c5c3_Err = templ.JoinStringErrs(service.Description)
		if templ_7745c5c3_Err != nil {
			return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/environment_card.templ`, Line: 16, Col: 91}
		}
		_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var4))
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 4, "</p></div></div><div class=\"flex flex-col items-end ml-4 flex-shrink-0\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		if service.Status == environment.StatusInstalled {
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 5, "<span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200\"><svg class=\"w-3 h-3 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\"><path fill-rule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clip-rule=\"evenodd\"></path></svg> ")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			var templ_7745c5c3_Var5 string
			templ_7745c5c3_Var5, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(currentLang, "environment.status.installed"))
			if templ_7745c5c3_Err != nil {
				return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/environment_card.templ`, Line: 25, Col: 59}
			}
			_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var5))
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 6, "</span>")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
		} else if service.Status == environment.StatusInstalling {
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 7, "<span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200\"><svg class=\"animate-spin w-3 h-3 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\"><circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle> <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path></svg> ")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			var templ_7745c5c3_Var6 string
			templ_7745c5c3_Var6, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(currentLang, "environment.status.installing"))
			if templ_7745c5c3_Err != nil {
				return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/environment_card.templ`, Line: 33, Col: 60}
			}
			_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var6))
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 8, "</span>")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
		} else {
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 9, "<span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200\">")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			var templ_7745c5c3_Var7 string
			templ_7745c5c3_Var7, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(currentLang, "environment.status.not_installed"))
			if templ_7745c5c3_Err != nil {
				return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/environment_card.templ`, Line: 37, Col: 63}
			}
			_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var7))
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 10, "</span>")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 11, "</div></div><div class=\"flex-1\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		if service.Status == environment.StatusInstalled {
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 12, "<div class=\"space-y-2\"><div class=\"flex justify-between text-sm\"><span class=\"text-gray-500 dark:text-gray-400\">")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			var templ_7745c5c3_Var8 string
			templ_7745c5c3_Var8, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(currentLang, "environment.version"))
			if templ_7745c5c3_Err != nil {
				return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/environment_card.templ`, Line: 47, Col: 97}
			}
			_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var8))
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 13, ":</span> <span class=\"text-gray-900 dark:text-gray-100\">")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			var templ_7745c5c3_Var9 string
			templ_7745c5c3_Var9, templ_7745c5c3_Err = templ.JoinStringErrs(service.Version)
			if templ_7745c5c3_Err != nil {
				return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/environment_card.templ`, Line: 48, Col: 70}
			}
			_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var9))
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 14, "</span></div>")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			if service.Port > 0 {
				templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 15, "<div class=\"flex justify-between text-sm\"><span class=\"text-gray-500 dark:text-gray-400\">")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				var templ_7745c5c3_Var10 string
				templ_7745c5c3_Var10, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(currentLang, "environment.port"))
				if templ_7745c5c3_Err != nil {
					return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/environment_card.templ`, Line: 52, Col: 95}
				}
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var10))
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 16, ":</span> <span class=\"text-gray-900 dark:text-gray-100\">")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				var templ_7745c5c3_Var11 string
				templ_7745c5c3_Var11, templ_7745c5c3_Err = templ.JoinStringErrs(strconv.Itoa(service.Port))
				if templ_7745c5c3_Err != nil {
					return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/environment_card.templ`, Line: 53, Col: 82}
				}
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var11))
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 17, "</span></div>")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
			}
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 18, "<div class=\"flex justify-between text-sm\"><span class=\"text-gray-500 dark:text-gray-400\">")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			var templ_7745c5c3_Var12 string
			templ_7745c5c3_Var12, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(currentLang, "environment.status"))
			if templ_7745c5c3_Err != nil {
				return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/environment_card.templ`, Line: 57, Col: 96}
			}
			_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var12))
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 19, ":</span> ")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			if service.IsRunning {
				templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 20, "<span class=\"text-green-600 dark:text-green-400 font-medium\">")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				var templ_7745c5c3_Var13 string
				templ_7745c5c3_Var13, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(currentLang, "environment.status.running"))
				if templ_7745c5c3_Err != nil {
					return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/environment_card.templ`, Line: 59, Col: 119}
				}
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var13))
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 21, "</span>")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
			} else {
				templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 22, "<span class=\"text-red-600 dark:text-red-400 font-medium\">")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				var templ_7745c5c3_Var14 string
				templ_7745c5c3_Var14, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(currentLang, "environment.status.stopped"))
				if templ_7745c5c3_Err != nil {
					return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/environment_card.templ`, Line: 61, Col: 115}
				}
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var14))
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 23, "</span>")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
			}
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 24, "</div></div>")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 25, "</div><div class=\"mt-4 pt-4 border-t border-gray-200 dark:border-gray-600\">")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		if service.Status == environment.StatusInstalled {
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 26, "<div class=\"flex space-x-2\">")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			if !service.IsRunning {
				templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 27, "<button class=\"flex-1 bg-green-600 dark:bg-green-700 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-green-700 dark:hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500\" data-service=\"")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				var templ_7745c5c3_Var15 string
				templ_7745c5c3_Var15, templ_7745c5c3_Err = templ.JoinStringErrs(service.Name)
				if templ_7745c5c3_Err != nil {
					return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/environment_card.templ`, Line: 74, Col: 34}
				}
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var15))
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 28, "\" data-action=\"start\">")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				var templ_7745c5c3_Var16 string
				templ_7745c5c3_Var16, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(currentLang, "environment.action.start"))
				if templ_7745c5c3_Err != nil {
					return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/environment_card.templ`, Line: 76, Col: 56}
				}
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var16))
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 29, "</button> ")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
			} else {
				templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 30, "<button class=\"flex-1 bg-red-600 dark:bg-red-700 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-red-700 dark:hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500\" data-service=\"")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				var templ_7745c5c3_Var17 string
				templ_7745c5c3_Var17, templ_7745c5c3_Err = templ.JoinStringErrs(service.Name)
				if templ_7745c5c3_Err != nil {
					return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/environment_card.templ`, Line: 81, Col: 34}
				}
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var17))
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 31, "\" data-action=\"stop\">")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				var templ_7745c5c3_Var18 string
				templ_7745c5c3_Var18, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(currentLang, "environment.action.stop"))
				if templ_7745c5c3_Err != nil {
					return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/environment_card.templ`, Line: 83, Col: 55}
				}
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var18))
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 32, "</button> ")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
			}
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 33, "<button class=\"flex-1 bg-blue-600 dark:bg-blue-700 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500\" data-service=\"")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			var templ_7745c5c3_Var19 string
			templ_7745c5c3_Var19, templ_7745c5c3_Err = templ.JoinStringErrs(service.Name)
			if templ_7745c5c3_Err != nil {
				return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/environment_card.templ`, Line: 88, Col: 33}
			}
			_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var19))
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 34, "\" data-action=\"restart\">")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			var templ_7745c5c3_Var20 string
			templ_7745c5c3_Var20, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(currentLang, "environment.action.restart"))
			if templ_7745c5c3_Err != nil {
				return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/environment_card.templ`, Line: 90, Col: 57}
			}
			_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var20))
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 35, "</button> ")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			if service.Name == "php" {
				templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 36, "<button class=\"bg-green-600 dark:bg-green-700 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-green-700 dark:hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500\" data-service=\"")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				var templ_7745c5c3_Var21 string
				templ_7745c5c3_Var21, templ_7745c5c3_Err = templ.JoinStringErrs(service.Name)
				if templ_7745c5c3_Err != nil {
					return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/environment_card.templ`, Line: 95, Col: 34}
				}
				_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var21))
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
				templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 37, "\" data-action=\"extensions\">扩展</button> ")
				if templ_7745c5c3_Err != nil {
					return templ_7745c5c3_Err
				}
			}
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 38, "<button class=\"bg-gray-600 dark:bg-gray-700 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-700 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500\" data-service=\"")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			var templ_7745c5c3_Var22 string
			templ_7745c5c3_Var22, templ_7745c5c3_Err = templ.JoinStringErrs(service.Name)
			if templ_7745c5c3_Err != nil {
				return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/environment_card.templ`, Line: 102, Col: 33}
			}
			_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var22))
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 39, "\" data-action=\"uninstall\">")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			var templ_7745c5c3_Var23 string
			templ_7745c5c3_Var23, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(currentLang, "environment.action.uninstall"))
			if templ_7745c5c3_Err != nil {
				return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/environment_card.templ`, Line: 104, Col: 59}
			}
			_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var23))
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 40, "</button></div>")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
		} else {
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 41, "<button class=\"w-full bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500\" data-service=\"")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			var templ_7745c5c3_Var24 string
			templ_7745c5c3_Var24, templ_7745c5c3_Err = templ.JoinStringErrs(service.Name)
			if templ_7745c5c3_Err != nil {
				return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/environment_card.templ`, Line: 110, Col: 32}
			}
			_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var24))
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 42, "\" data-action=\"install\">")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			var templ_7745c5c3_Var25 string
			templ_7745c5c3_Var25, templ_7745c5c3_Err = templ.JoinStringErrs(i18n.T(currentLang, "environment.action.install"))
			if templ_7745c5c3_Err != nil {
				return templ.Error{Err: templ_7745c5c3_Err, FileName: `internal/templates/components/environment_card.templ`, Line: 112, Col: 56}
			}
			_, templ_7745c5c3_Err = templ_7745c5c3_Buffer.WriteString(templ.EscapeString(templ_7745c5c3_Var25))
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
			templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 43, "</button>")
			if templ_7745c5c3_Err != nil {
				return templ_7745c5c3_Err
			}
		}
		templ_7745c5c3_Err = templruntime.WriteString(templ_7745c5c3_Buffer, 44, "</div></div><script>\n\t\t// Use event delegation for service buttons\n\t\tdocument.addEventListener('click', function(e) {\n\t\t\tif (e.target.dataset.service && e.target.dataset.action) {\n\t\t\t\tconst serviceName = e.target.dataset.service;\n\t\t\t\tconst action = e.target.dataset.action;\n\n\t\t\t\tswitch(action) {\n\t\t\t\t\tcase 'install':\n\t\t\t\t\t\tinstallService(serviceName);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'uninstall':\n\t\t\t\t\t\tuninstallService(serviceName);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'start':\n\t\t\t\t\t\tstartService(serviceName);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'stop':\n\t\t\t\t\t\tstopService(serviceName);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'restart':\n\t\t\t\t\t\trestartService(serviceName);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'extensions':\n\t\t\t\t\t\tshowPHPExtensions();\n\t\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\n\t\tfunction installService(serviceName) {\n\t\t\t// 检查是否已经在安装中\n\t\t\tif (document.querySelector(`#progress-${serviceName}`)) {\n\t\t\t\treturn; // 已经在安装中，忽略重复点击\n\t\t\t}\n\n\t\t\t// 使用自定义确认弹窗\n\t\t\tshowConfirmModal(\n\t\t\t\t'确认安装',\n\t\t\t\t`您确定要安装 ${serviceName} 吗？`,\n\t\t\t\t'安装',\n\t\t\t\tfunction() {\n\t\t\t\t\t// 禁用安装按钮\n\t\t\t\t\tconst installBtn = document.querySelector(`[data-service=\"${serviceName}\"][data-action=\"install\"]`);\n\t\t\t\t\tif (installBtn) {\n\t\t\t\t\t\tinstallBtn.disabled = true;\n\t\t\t\t\t\tinstallBtn.textContent = 'Installing...';\n\t\t\t\t\t}\n\n\t\t\t\t\t// 显示安装进度\n\t\t\t\t\tshowInstallProgress(serviceName);\n\n\t\t\t\t\tfetch(`/api/environment/install`, {\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\theaders: {\n\t\t\t\t\t\t\t'Content-Type': 'application/json',\n\t\t\t\t\t\t},\n\t\t\t\t\t\tbody: JSON.stringify({\n\t\t\t\t\t\t\tname: serviceName,\n\t\t\t\t\t\t\tversion: 'latest'\n\t\t\t\t\t\t})\n\t\t\t\t\t})\n\t\t\t\t\t.then(response => response.json())\n\t\t\t\t\t.then(data => {\n\t\t\t\t\t\tif (data.success) {\n\t\t\t\t\t\t\t// 开始轮询进度\n\t\t\t\t\t\t\tpollInstallProgress(serviceName);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\thideInstallProgress(serviceName);\n\t\t\t\t\t\t\tenableInstallButton(serviceName);\n\t\t\t\t\t\t\tshowNotification('安装失败: ' + data.error, 'error');\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t\t.catch(error => {\n\t\t\t\t\t\thideInstallProgress(serviceName);\n\t\t\t\t\t\tenableInstallButton(serviceName);\n\t\t\t\t\t\tshowNotification('安装失败: ' + error.message, 'error');\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t);\n\t\t}\n\n\t\tfunction showInstallProgress(serviceName) {\n\t\t\t// 检查是否已经有进度显示\n\t\t\tif (document.querySelector(`#progress-${serviceName}`)) {\n\t\t\t\treturn; // 已经有进度显示，不重复添加\n\t\t\t}\n\n\t\t\t// 找到对应的服务卡片\n\t\t\tconst serviceCard = document.querySelector(`[data-service=\"${serviceName}\"][data-action=\"install\"]`).closest('.bg-white, .dark\\\\:bg-gray-800');\n\t\t\tif (serviceCard) {\n\t\t\t\t// 添加进度显示\n\t\t\t\tconst progressHtml = `\n\t\t\t\t\t<div class=\"install-progress install-progress-${serviceName} mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md\">\n\t\t\t\t\t\t<div class=\"flex items-center justify-between mb-2\">\n\t\t\t\t\t\t\t<span class=\"text-sm font-medium text-blue-700 dark:text-blue-300\">Installing ${serviceName}...</span>\n\t\t\t\t\t\t\t<span class=\"text-sm text-blue-600 dark:text-blue-400\" id=\"progress-${serviceName}\">0%</span>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class=\"w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2\">\n\t\t\t\t\t\t\t<div class=\"bg-blue-600 dark:bg-blue-400 h-2 rounded-full transition-all duration-300\"\n\t\t\t\t\t\t\t\t id=\"progress-bar-${serviceName}\" style=\"width: 0%\"></div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class=\"text-xs text-blue-600 dark:text-blue-400 mt-1\" id=\"progress-message-${serviceName}\">\n\t\t\t\t\t\t\tPreparing installation...\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t`;\n\t\t\t\tserviceCard.insertAdjacentHTML('beforeend', progressHtml);\n\t\t\t}\n\t\t}\n\n\t\tfunction hideInstallProgress(serviceName) {\n\t\t\tif (serviceName) {\n\t\t\t\t// 隐藏特定服务的进度\n\t\t\t\tdocument.querySelectorAll(`.install-progress-${serviceName}`).forEach(el => el.remove());\n\t\t\t} else {\n\t\t\t\t// 隐藏所有进度\n\t\t\t\tdocument.querySelectorAll('.install-progress').forEach(el => el.remove());\n\t\t\t}\n\t\t}\n\n\t\tfunction enableInstallButton(serviceName) {\n\t\t\tconst installBtn = document.querySelector(`[data-service=\"${serviceName}\"][data-action=\"install\"]`);\n\t\t\tif (installBtn) {\n\t\t\t\tinstallBtn.disabled = false;\n\t\t\t\tinstallBtn.textContent = '安装';\n\t\t\t}\n\t\t}\n\n\t\tfunction updateProgress(serviceName, progress, message) {\n\t\t\tconst progressPercent = document.getElementById(`progress-${serviceName}`);\n\t\t\tconst progressBar = document.getElementById(`progress-bar-${serviceName}`);\n\t\t\tconst progressMessage = document.getElementById(`progress-message-${serviceName}`);\n\n\t\t\tif (progressPercent) progressPercent.textContent = progress + '%';\n\t\t\tif (progressBar) progressBar.style.width = progress + '%';\n\t\t\tif (progressMessage) progressMessage.textContent = message;\n\t\t}\n\n\t\tfunction pollInstallProgress(serviceName) {\n\t\t\tconst interval = setInterval(() => {\n\t\t\t\tfetch('/api/environment/progress')\n\t\t\t\t\t.then(response => response.json())\n\t\t\t\t\t.then(data => {\n\t\t\t\t\t\tif (data.success && data.data && data.data.environment === serviceName) {\n\t\t\t\t\t\t\tupdateProgress(serviceName, data.data.progress, data.data.message);\n\n\t\t\t\t\t\t\tif (data.data.status === 'completed') {\n\t\t\t\t\t\t\t\tclearInterval(interval);\n\t\t\t\t\t\t\t\thideInstallProgress(serviceName);\n\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\tlocation.reload();\n\t\t\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t\t\t} else if (data.data.status === 'error') {\n\t\t\t\t\t\t\t\tclearInterval(interval);\n\t\t\t\t\t\t\t\thideInstallProgress(serviceName);\n\t\t\t\t\t\t\t\tenableInstallButton(serviceName);\n\t\t\t\t\t\t\t\tshowNotification('安装失败: ' + data.data.message, 'error');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if (data.success && !data.data) {\n\t\t\t\t\t\t\t// 没有进度数据，可能安装已完成\n\t\t\t\t\t\t\tclearInterval(interval);\n\t\t\t\t\t\t\thideInstallProgress(serviceName);\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\tlocation.reload();\n\t\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t\t.catch(error => {\n\t\t\t\t\t\tclearInterval(interval);\n\t\t\t\t\t\thideInstallProgress(serviceName);\n\t\t\t\t\t\tenableInstallButton(serviceName);\n\t\t\t\t\t\tshowNotification('进度跟踪失败: ' + error.message, 'error');\n\t\t\t\t\t});\n\t\t\t}, 1000);\n\t\t}\n\n\t\tfunction uninstallService(serviceName) {\n\t\t\t// 使用自定义确认弹窗\n\t\t\tshowConfirmModal(\n\t\t\t\t'确认卸载',\n\t\t\t\t`您确定要卸载 ${serviceName} 吗？此操作无法撤销。`,\n\t\t\t\t'卸载',\n\t\t\t\tfunction() {\n\t\t\t\t\t// 执行卸载操作\n\t\t\t\t\tfetch(`/api/environment/uninstall`, {\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\theaders: {\n\t\t\t\t\t\t\t'Content-Type': 'application/json',\n\t\t\t\t\t\t},\n\t\t\t\t\t\tbody: JSON.stringify({\n\t\t\t\t\t\t\tname: serviceName\n\t\t\t\t\t\t})\n\t\t\t\t\t})\n\t\t\t\t\t.then(response => response.json())\n\t\t\t\t\t.then(data => {\n\t\t\t\t\t\tif (data.success) {\n\t\t\t\t\t\t\tlocation.reload();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tshowNotification('卸载失败: ' + data.error, 'error');\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t\t.catch(error => {\n\t\t\t\t\t\tshowNotification('卸载失败: ' + error.message, 'error');\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t);\n\t\t}\n\n\t\tfunction startService(serviceName) {\n\t\t\tfetch(`/api/environment/start`, {\n\t\t\t\tmethod: 'POST',\n\t\t\t\theaders: {\n\t\t\t\t\t'Content-Type': 'application/json',\n\t\t\t\t},\n\t\t\t\tbody: JSON.stringify({\n\t\t\t\t\tservice: serviceName\n\t\t\t\t})\n\t\t\t})\n\t\t\t.then(response => response.json())\n\t\t\t.then(data => {\n\t\t\t\tif (data.success) {\n\t\t\t\t\tshowNotification('服务启动成功', 'success');\n\t\t\t\t\tlocation.reload();\n\t\t\t\t} else {\n\t\t\t\t\tshowNotification('服务启动失败: ' + data.error, 'error');\n\t\t\t\t}\n\t\t\t})\n\t\t\t.catch(error => {\n\t\t\t\tshowNotification('服务启动失败: ' + error.message, 'error');\n\t\t\t});\n\t\t}\n\n\t\tfunction stopService(serviceName) {\n\t\t\t// 使用自定义确认弹窗\n\t\t\tshowConfirmModal(\n\t\t\t\t'确认停止服务',\n\t\t\t\t`您确定要停止 ${serviceName} 服务吗？`,\n\t\t\t\t'停止',\n\t\t\t\tfunction() {\n\t\t\t\t\tfetch(`/api/environment/stop`, {\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\theaders: {\n\t\t\t\t\t\t\t'Content-Type': 'application/json',\n\t\t\t\t\t\t},\n\t\t\t\t\t\tbody: JSON.stringify({\n\t\t\t\t\t\t\tname: serviceName\n\t\t\t\t\t\t})\n\t\t\t\t\t})\n\t\t\t\t\t.then(response => response.json())\n\t\t\t\t\t.then(data => {\n\t\t\t\t\t\tif (data.success) {\n\t\t\t\t\t\t\tshowNotification('服务停止成功', 'success');\n\t\t\t\t\t\t\tlocation.reload();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tshowNotification('服务停止失败: ' + data.error, 'error');\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t\t.catch(error => {\n\t\t\t\t\t\tshowNotification('服务停止失败: ' + error.message, 'error');\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t);\n\t\t}\n\n\t\tfunction restartService(serviceName) {\n\t\t\tfetch(`/api/environment/restart`, {\n\t\t\t\tmethod: 'POST',\n\t\t\t\theaders: {\n\t\t\t\t\t'Content-Type': 'application/json',\n\t\t\t\t},\n\t\t\t\tbody: JSON.stringify({\n\t\t\t\t\tname: serviceName\n\t\t\t\t})\n\t\t\t})\n\t\t\t.then(response => response.json())\n\t\t\t.then(data => {\n\t\t\t\tif (data.success) {\n\t\t\t\t\tshowNotification('服务重启成功', 'success');\n\t\t\t\t\tlocation.reload();\n\t\t\t\t} else {\n\t\t\t\t\tshowNotification('服务重启失败: ' + data.error, 'error');\n\t\t\t\t}\n\t\t\t})\n\t\t\t.catch(error => {\n\t\t\t\tshowNotification('服务重启失败: ' + error.message, 'error');\n\t\t\t});\n\t\t}\n\n\t\t// 显示 PHP 扩展管理\n\t\tfunction showPHPExtensions() {\n\t\t\t// 滚动到 PHP 扩展区域\n\t\t\tconst extensionsSection = document.getElementById('php-extensions-section');\n\t\t\tif (extensionsSection) {\n\t\t\t\textensionsSection.scrollIntoView({\n\t\t\t\t\tbehavior: 'smooth',\n\t\t\t\t\tblock: 'start'\n\t\t\t\t});\n\n\t\t\t\t// 高亮显示扩展区域\n\t\t\t\textensionsSection.classList.add('ring-2', 'ring-blue-500', 'ring-opacity-50');\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\textensionsSection.classList.remove('ring-2', 'ring-blue-500', 'ring-opacity-50');\n\t\t\t\t}, 2000);\n\t\t\t}\n\t\t}\n\t</script>")
		if templ_7745c5c3_Err != nil {
			return templ_7745c5c3_Err
		}
		return nil
	})
}

var _ = templruntime.GeneratedTemplate
