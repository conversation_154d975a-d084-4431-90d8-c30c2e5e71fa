package components

templ Modal() {
	<!-- 模态框容器 -->
	<div id="modal" x-show="modalOpen" x-transition class="fixed inset-0 z-50 overflow-y-auto hidden">
		<div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
			<div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
			<span class="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>
			<div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
				<div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
					<div class="sm:flex sm:items-start">
						<div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
							<h3 id="modal-title" class="text-lg leading-6 font-medium text-gray-900 mb-4">
								模态框标题
							</h3>
							<div id="modal-content">
								<!-- 动态内容 -->
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
}
