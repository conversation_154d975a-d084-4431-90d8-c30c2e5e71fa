package layouts

import "server-panel/internal/templates/components"

templ Base(title string, username string, currentLang string) {
	<!DOCTYPE html>
	<html lang="zh-CN" x-data="app" x-init="init()" :class="{ 'dark': isDark }">
	<head>
		<meta charset="UTF-8"/>
		<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
		<title>{ title }</title>

		<!-- 防止暗黑模式闪白 -->
		<script>
			// 在页面加载前立即应用暗黑模式
			(function() {
				const theme = localStorage.getItem('theme') || 'dark';
				if (theme === 'dark') {
					document.documentElement.classList.add('dark');
				}
			})();
		</script>

		<!-- CSS -->
		<link rel="stylesheet" href="/static/css/output.css?v=20250727-sse-debug"/>
		<link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🖥️</text></svg>"/>
	</head>
	<body class="bg-gray-50 font-sans antialiased dark:bg-gray-900 transition-colors duration-200">
		@components.Navigation(username, currentLang)
		
		<!-- 主内容区域 -->
		<main class="pt-16">
			<div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
				{ children... }
			</div>
		</main>

		@components.Modal()

		<!-- JavaScript -->
		<script src="/static/js/htmx.min.js?v=working"></script>
		<script src="/static/js/alpine.min.js?v=working" defer></script>

		<script>
			// DigWis Panel v3.6 - 原生Go版本 (手动测试按钮)
			console.log('🚀 DigWis Panel v3.6 加载中 - 手动测试按钮!');

			// Alpine.js 全局状态
			document.addEventListener('alpine:init', () => {
				Alpine.data('app', () => ({
					modalOpen: false,
					isDark: localStorage.getItem('theme') === 'dark' ||
							(!localStorage.getItem('theme') && window.matchMedia('(prefers-color-scheme: dark)').matches),

					init() {
						console.log('DigWis Panel 已加载');
						// 初始化主题
						this.updateTheme();
					},

					toggleTheme() {
						this.isDark = !this.isDark;
						this.updateTheme();
					},

					updateTheme() {
						if (this.isDark) {
							localStorage.setItem('theme', 'dark');
							document.documentElement.classList.add('dark');
						} else {
							localStorage.setItem('theme', 'light');
							document.documentElement.classList.remove('dark');
						}
					},

					openModal(title) {
						document.getElementById('modal-title').textContent = title;
						this.modalOpen = true;
						document.getElementById('modal').classList.remove('hidden');
					},

					closeModal() {
						this.modalOpen = false;
						document.getElementById('modal').classList.add('hidden');
					}
				}));

				// 导航状态管理
				Alpine.data('navigation', () => ({
					currentPath: window.location.pathname,

					init() {
						// 监听页面变化（如果使用 HTMX 或其他 SPA 路由）
						window.addEventListener('popstate', () => {
							this.currentPath = window.location.pathname;
						});
					},

					setActiveNav() {
						this.currentPath = window.location.pathname;
					},

					isActive(path) {
						return this.currentPath === path ||
							   (path === '/dashboard' && this.currentPath === '/');
					}
				}));
			});

			// HTMX配置 - 优化版本
			htmx.config.globalViewTransitions = false; // 禁用视图转换以提高性能
			htmx.config.defaultSwapStyle = 'innerHTML';
			htmx.config.defaultSwapDelay = 50; // 减少延迟
			htmx.config.timeout = 10000; // 10秒超时
			htmx.config.historyCacheSize = 5; // 限制历史缓存大小

			// SSE相关变量 - 优化版本
			let eventSource = null;
			let cpuData = [];
			let reconnectAttempts = 0;
			const maxReconnectAttempts = 3; // 减少重连次数
			let lastUpdateTime = 0;
			const updateThrottle = 1000; // 限制更新频率为1秒

			// 更新连接状态
			function updateConnectionStatus(status, text) {
				console.log('连接状态更新:', status, text);
				const statusEl = document.getElementById('connection-status');
				const textEl = document.getElementById('connection-text');

				if (statusEl && textEl) {
					statusEl.className = 'w-2 h-2 rounded-full ' +
						(status === 'connected' ? 'bg-green-400' :
						 status === 'error' ? 'bg-red-400' : 'bg-yellow-400');
					textEl.textContent = text;
				} else {
					console.log('连接状态元素未找到，状态:', status, text);
				}
			}

			// 更新系统统计 - 优化版本，添加节流机制
			function updateSystemStats(stats) {
				const now = Date.now();
				if (now - lastUpdateTime < updateThrottle) {
					return; // 跳过过于频繁的更新
				}
				lastUpdateTime = now;

				console.log('更新系统统计，数据结构:', stats);

				// 批量更新DOM，减少重排重绘
				requestAnimationFrame(() => {
					updateCPUCard(stats.cpu);
					updateMemoryCard(stats.memory);
					updateDiskCard(stats.disk);
					updateNetworkCard(stats.network);
					updateCPUChart(stats.cpu ? stats.cpu.usage : 0);
					updateMemoryDetails(stats.memory);
				});
			}

			// 更新CPU卡片
			function updateCPUCard(cpu) {
				console.log('更新CPU卡片:', cpu);
				const usageEl = document.getElementById('cpu-usage');
				const progressEl = document.getElementById('cpu-progress');

				console.log('CPU元素:', usageEl, progressEl);
				if (usageEl && cpu && typeof cpu.usage === 'number') {
					usageEl.textContent = cpu.usage.toFixed(1) + '%';
					console.log('CPU使用率已更新:', cpu.usage.toFixed(1) + '%');
				}
				if (progressEl && cpu && typeof cpu.usage === 'number') {
					progressEl.style.width = cpu.usage.toFixed(1) + '%';
					console.log('CPU进度条已更新:', cpu.usage.toFixed(1) + '%');
				}
			}

			// 更新内存卡片
			function updateMemoryCard(memory) {
				const usageEl = document.getElementById('memory-usage');
				const progressEl = document.getElementById('memory-progress');

				if (usageEl && memory && typeof memory.usage === 'number') {
					usageEl.textContent = memory.usage.toFixed(1) + '%';
				}
				if (progressEl && memory && typeof memory.usage === 'number') {
					progressEl.style.width = memory.usage.toFixed(1) + '%';
				}
			}

			// 更新磁盘卡片
			function updateDiskCard(disk) {
				const usageEl = document.getElementById('disk-usage');
				const progressEl = document.getElementById('disk-progress');

				if (usageEl && disk && typeof disk.usage === 'number') {
					usageEl.textContent = disk.usage.toFixed(1) + '%';
				}
				if (progressEl && disk && typeof disk.usage === 'number') {
					progressEl.style.width = disk.usage.toFixed(1) + '%';
				}
			}

			// 更新网络卡片
			function updateNetworkCard(network) {
				const usageEl = document.getElementById('network-usage');
				const detailsEl = document.getElementById('network-details');

				if (usageEl && network) {
					const totalBytes = (network.bytes_received || 0) + (network.bytes_sent || 0);
					usageEl.textContent = formatBytes(totalBytes);
				}
				if (detailsEl && network) {
					const received = formatBytes(network.bytes_received || 0);
					const sent = formatBytes(network.bytes_sent || 0);
					detailsEl.textContent = '↓ ' + received + ' ↑ ' + sent;
				}
			}

			// 更新CPU图表
			function updateCPUChart(cpuUsage) {
				cpuData.push(cpuUsage);
				if (cpuData.length > 30) {
					cpuData.shift();
				}

				const canvas = document.getElementById('cpu-canvas');
				if (!canvas) return;

				const ctx = canvas.getContext('2d');
				ctx.clearRect(0, 0, canvas.width, canvas.height);

				// 绘制网格
				ctx.strokeStyle = '#e5e7eb';
				ctx.lineWidth = 1;
				for (let i = 0; i <= 10; i++) {
					const y = (canvas.height / 10) * i;
					ctx.beginPath();
					ctx.moveTo(0, y);
					ctx.lineTo(canvas.width, y);
					ctx.stroke();
				}

				// 绘制CPU使用率线
				if (cpuData.length > 1) {
					ctx.strokeStyle = '#3b82f6';
					ctx.lineWidth = 2;
					ctx.beginPath();

					for (let i = 0; i < cpuData.length; i++) {
						const x = (canvas.width / (cpuData.length - 1)) * i;
						const y = canvas.height - (cpuData[i] / 100) * canvas.height;

						if (i === 0) {
							ctx.moveTo(x, y);
						} else {
							ctx.lineTo(x, y);
						}
					}
					ctx.stroke();
				}
			}

			// 更新内存详细信息
			function updateMemoryDetails(memory) {
				const usageEl = document.getElementById('memory-usage-detail');
				const detailsEl = document.getElementById('memory-details');

				if (usageEl && memory && typeof memory.usage === 'number') {
					usageEl.textContent = memory.usage.toFixed(1) + '%';
				}

				if (detailsEl && memory) {
					// 获取当前语言
					const currentLang = getCookie('language') || 'zh';

					// 根据语言设置文本
					const labels = {
						zh: {
							used: '已使用',
							available: '可用',
							free: '空闲'
						},
						en: {
							used: 'Used',
							available: 'Available',
							free: 'Free'
						}
					};

					const lang = labels[currentLang] || labels.zh;

					const detailsHTML =
						'<div class="space-y-3">' +
							'<div class="flex justify-between items-center">' +
								'<span class="text-sm text-gray-600 dark:text-gray-400">' + lang.used + '</span>' +
								'<span class="text-sm font-medium text-gray-900 dark:text-gray-100">' + formatBytes(memory.used || 0) + ' / ' + formatBytes(memory.total || 0) + '</span>' +
							'</div>' +
							'<div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">' +
								'<div class="bg-green-500 h-2 rounded-full transition-all duration-500" style="width: ' + (memory.usage || 0) + '%"></div>' +
							'</div>' +
							'<div class="flex justify-between items-center">' +
								'<span class="text-sm text-gray-600 dark:text-gray-400">' + lang.available + '</span>' +
								'<span class="text-sm font-medium text-gray-900 dark:text-gray-100">' + formatBytes(memory.available || 0) + '</span>' +
							'</div>' +
							'<div class="flex justify-between items-center">' +
								'<span class="text-sm text-gray-600 dark:text-gray-400">' + lang.free + '</span>' +
								'<span class="text-sm font-medium text-gray-900 dark:text-gray-100">' + formatBytes(memory.free || 0) + '</span>' +
							'</div>' +
						'</div>';
					detailsEl.innerHTML = detailsHTML;
				}
			}

			// 工具函数
			function formatBytes(bytes) {
				if (bytes === 0) return '0 B';
				const k = 1024;
				const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
				const i = Math.floor(Math.log(bytes) / Math.log(k));
				return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
			}

			// 获取Cookie值
			function getCookie(name) {
				const value = `; ${document.cookie}`;
				const parts = value.split(`; ${name}=`);
				if (parts.length === 2) return parts.pop().split(';').shift();
				return null;
			}

			// 初始化SSE连接 - 标准库实现
			function initSSE() {
				if (eventSource) {
					console.log('关闭现有SSE连接');
					eventSource.close();
				}

				console.log('🔄 正在初始化SSE连接...');
				console.log('当前页面URL:', window.location.href);
				console.log('SSE URL:', '/api/sse/stats');
				updateConnectionStatus('connecting', '连接中...');

				// 创建EventSource连接
				try {
					eventSource = new EventSource('/api/sse/stats');
					console.log('✅ EventSource对象已创建');
					console.log('EventSource readyState:', eventSource.readyState);
					console.log('EventSource URL:', eventSource.url);

					// 立即检查连接状态
					setTimeout(() => {
						console.log('1秒后 EventSource readyState:', eventSource.readyState);
						console.log('EventSource.CONNECTING =', EventSource.CONNECTING);
						console.log('EventSource.OPEN =', EventSource.OPEN);
						console.log('EventSource.CLOSED =', EventSource.CLOSED);
					}, 1000);
				} catch (error) {
					console.error('❌ 创建EventSource失败:', error);
					return;
				}

				// 连接打开事件（标准事件）
				eventSource.onopen = function(event) {
					console.log('🔗 EventSource onopen 触发:', event);
					console.log('连接状态:', eventSource.readyState);
				};

				// 连接建立事件（自定义事件）
				eventSource.addEventListener('connected', function(event) {
					console.log('🎉 SSE连接已建立:', event.data);
					const currentLang = getCookie('language') || 'zh';
					const connectedText = currentLang === 'en' ? 'Connected' : '已连接';
					updateConnectionStatus('connected', connectedText);
					reconnectAttempts = 0; // 重置重连计数
				});

				// 系统统计数据事件
				eventSource.addEventListener('stats', function(event) {
					console.log('📊 收到系统统计数据:', event.data);
					try {
						const stats = JSON.parse(event.data);
						console.log('📈 解析后的统计数据:', stats);
						updateSystemStats(stats);
					} catch (error) {
						console.error('❌ 解析统计数据失败:', error, '原始数据:', event.data);
					}
				});

				// 心跳包事件 - 保持连接活跃
				eventSource.addEventListener('heartbeat', function(event) {
					console.log('💓 收到心跳包:', event.data);
					// 心跳包不需要更新UI，只是保持连接
				});

				// 错误事件
				eventSource.addEventListener('error', function(event) {
					console.log('SSE错误事件:', event);
					if (event.data) {
						try {
							const errorData = JSON.parse(event.data);
							console.error('SSE错误:', errorData.error);
							updateConnectionStatus('error', '错误: ' + errorData.error);
						} catch (e) {
							console.error('解析错误数据失败:', e);
						}
					}
				});

				// 连接错误处理
				eventSource.onerror = function(event) {
					console.error('SSE连接错误:', event);
					updateConnectionStatus('error', '连接错误');

					// 自动重连逻辑
					if (reconnectAttempts < maxReconnectAttempts) {
						reconnectAttempts++;
						const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000); // 指数退避，最大30秒
						console.log(`${delay/1000}秒后尝试第${reconnectAttempts}次重连...`);
						updateConnectionStatus('connecting', `重连中 (${reconnectAttempts}/${maxReconnectAttempts})`);

						setTimeout(() => {
							if (eventSource.readyState === EventSource.CLOSED) {
								initSSE();
							}
						}, delay);
					} else {
						console.error('达到最大重连次数，停止重连');
						updateConnectionStatus('error', '连接失败');
					}
				};

				// 通用消息处理（兜底）
				eventSource.onmessage = function(event) {
					console.log('收到SSE消息:', event.data);
					try {
						const data = JSON.parse(event.data);
						if (data.type === 'system_stats') {
							updateSystemStats(data.data);
						}
					} catch (error) {
						console.error('解析SSE数据失败:', error);
					}
				};
			}

			// 手动测试SSE连接
			function testSSEConnection() {
				console.log('🧪 手动测试SSE连接...');

				if (eventSource) {
					eventSource.close();
					console.log('关闭现有连接');
				}

				const testSource = new EventSource('/api/sse/stats');

				testSource.onopen = function(event) {
					console.log('✅ 手动测试 - EventSource连接打开');
					alert('✅ SSE连接成功建立！');
				};

				testSource.addEventListener('stats', function(event) {
					console.log('📊 手动测试 - 收到数据:', event.data);
					const stats = JSON.parse(event.data);
					alert('📊 收到数据！CPU: ' + stats.cpu.usage + '%');
					testSource.close();
				});

				testSource.onerror = function(event) {
					console.log('❌ 手动测试 - 连接错误');
					alert('❌ SSE连接失败！');
				};

				// 10秒后自动关闭
				setTimeout(() => {
					testSource.close();
					console.log('🔒 手动测试连接已关闭');
				}, 10000);
			}

			// 页面加载时初始化
			document.addEventListener('DOMContentLoaded', function() {
				console.log('页面加载完成');
				console.log('当前页面URL:', window.location.pathname);

				// 检查所有相关元素
				const cpuElement = document.getElementById('cpu-usage');
				const memoryElement = document.getElementById('memory-usage');
				const diskElement = document.getElementById('disk-usage');
				const networkElement = document.getElementById('network-usage');

				console.log('页面元素检查:');
				console.log('- CPU元素:', cpuElement);
				console.log('- 内存元素:', memoryElement);
				console.log('- 磁盘元素:', diskElement);
				console.log('- 网络元素:', networkElement);

				// 如果在仪表板页面，初始化SSE连接
				if (cpuElement) {
					console.log('✅ 检测到仪表板页面，初始化SSE连接');
					initSSE();
				} else {
					console.log('❌ 未检测到仪表板页面');
				}
			});

			// 页面卸载时关闭SSE连接
			window.addEventListener('beforeunload', function() {
				if (eventSource) {
					console.log('关闭SSE连接');
					eventSource.close();
				}
			});
		</script>
	</body>
	</html>
}
