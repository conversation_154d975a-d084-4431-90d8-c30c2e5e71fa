#!/bin/bash

# DigWis Panel VPS 开发连接脚本
# 使用方法: ./scripts/dev-ssh.sh [命令]

VPS_HOST="parallels@***********"
VPS_PASSWORD="Zhao4426184，"
PROJECT_PATH="/media/psf/Linux-86/digwis-panel"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${BLUE}🚀 DigWis Panel VPS 开发工具${NC}"
    echo -e "${BLUE}=================================${NC}"
    echo ""
    echo -e "${GREEN}基本连接:${NC}"
    echo "  ./scripts/dev-ssh.sh                    # 直接连接到 VPS"
    echo "  ./scripts/dev-ssh.sh connect            # 同上"
    echo ""
    echo -e "${GREEN}Screen 开发环境:${NC}"
    echo "  ./scripts/dev-ssh.sh dev                # 启动/连接开发环境"
    echo "  ./scripts/dev-ssh.sh status             # 查看服务状态"
    echo "  ./scripts/dev-ssh.sh attach             # 连接到现有会话"
    echo "  ./scripts/dev-ssh.sh detach             # 分离会话(保持运行)"
    echo "  ./scripts/dev-ssh.sh stop               # 停止开发环境"
    echo "  ./scripts/dev-ssh.sh restart            # 重启开发环境"
    echo ""
    echo -e "${GREEN}快速命令:${NC}"
    echo "  ./scripts/dev-ssh.sh cmd 'ls -la'       # 执行单个命令"
    echo "  ./scripts/dev-ssh.sh logs               # 查看服务日志"
    echo ""
    echo -e "${YELLOW}💡 提示:${NC}"
    echo "  - 使用 Ctrl+A, D 分离 screen 会话"
    echo "  - 服务运行在 http://***********:9090"
    echo "  - 项目路径: ${PROJECT_PATH}"
}

# 检查 sshpass 是否安装
check_sshpass() {
    if ! command -v sshpass &> /dev/null; then
        echo -e "${RED}❌ sshpass 未安装${NC}"
        echo "请安装 sshpass: brew install sshpass"
        exit 1
    fi
}

# 基本 SSH 连接
ssh_connect() {
    echo -e "${GREEN}🔗 连接到 VPS...${NC}"
    sshpass -p "$VPS_PASSWORD" ssh -o StrictHostKeyChecking=no "$VPS_HOST"
}

# 执行远程命令
ssh_exec() {
    local cmd="$1"
    sshpass -p "$VPS_PASSWORD" ssh -o StrictHostKeyChecking=no "$VPS_HOST" "$cmd"
}

# 启动开发环境
dev_start() {
    echo -e "${GREEN}🚀 启动 Screen 开发环境...${NC}"
    ssh_exec "cd $PROJECT_PATH && make dev-screen"
}

# 查看状态
dev_status() {
    echo -e "${BLUE}📊 检查开发环境状态...${NC}"
    ssh_exec "cd $PROJECT_PATH && make dev-screen-status"
}

# 连接到现有会话
dev_attach() {
    echo -e "${GREEN}🔗 连接到开发会话...${NC}"
    sshpass -p "$VPS_PASSWORD" ssh -o StrictHostKeyChecking=no -t "$VPS_HOST" "cd $PROJECT_PATH && make dev-screen-attach"
}

# 分离会话
dev_detach() {
    echo -e "${YELLOW}🔌 分离开发会话...${NC}"
    ssh_exec "cd $PROJECT_PATH && make dev-screen-detach"
}

# 停止开发环境
dev_stop() {
    echo -e "${RED}🛑 停止开发环境...${NC}"
    ssh_exec "cd $PROJECT_PATH && make dev-screen-stop"
}

# 重启开发环境
dev_restart() {
    echo -e "${BLUE}🔄 重启开发环境...${NC}"
    ssh_exec "cd $PROJECT_PATH && make dev-screen-restart"
}

# 查看日志
dev_logs() {
    echo -e "${BLUE}📋 查看服务日志...${NC}"
    sshpass -p "$VPS_PASSWORD" ssh -o StrictHostKeyChecking=no -t "$VPS_HOST" "cd $PROJECT_PATH && screen -S digwis-dev -X hardcopy /tmp/screen.log && tail -f /tmp/screen.log 2>/dev/null || echo '请先启动开发环境'"
}

# 主逻辑
main() {
    check_sshpass
    
    case "${1:-connect}" in
        "help"|"-h"|"--help")
            show_help
            ;;
        "connect"|"")
            ssh_connect
            ;;
        "dev")
            dev_start
            ;;
        "status")
            dev_status
            ;;
        "attach")
            dev_attach
            ;;
        "detach")
            dev_detach
            ;;
        "stop")
            dev_stop
            ;;
        "restart")
            dev_restart
            ;;
        "logs")
            dev_logs
            ;;
        "cmd")
            if [ -z "$2" ]; then
                echo -e "${RED}❌ 请提供要执行的命令${NC}"
                echo "用法: $0 cmd 'your command'"
                exit 1
            fi
            echo -e "${BLUE}⚡ 执行命令: $2${NC}"
            ssh_exec "cd $PROJECT_PATH && $2"
            ;;
        *)
            echo -e "${RED}❌ 未知命令: $1${NC}"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

main "$@"
