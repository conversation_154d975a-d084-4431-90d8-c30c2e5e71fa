#!/bin/bash

# DigWis Panel 本地开发脚本
# 使用方法: ./scripts/dev-local.sh [命令]

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo -e "${BLUE}🖥️  DigWis Panel 本地开发工具${NC}"
    echo -e "${BLUE}=================================${NC}"
    echo ""
    echo -e "${GREEN}开发环境:${NC}"
    echo "  ./scripts/dev-local.sh dev               # 启动本地开发环境"
    echo "  ./scripts/dev-local.sh build             # 构建项目"
    echo "  ./scripts/dev-local.sh test              # 运行测试"
    echo ""
    echo -e "${GREEN}依赖管理:${NC}"
    echo "  ./scripts/dev-local.sh install           # 安装依赖"
    echo "  ./scripts/dev-local.sh install-air       # 安装 Air"
    echo "  ./scripts/dev-local.sh clean             # 清理构建文件"
    echo ""
    echo -e "${GREEN}工具:${NC}"
    echo "  ./scripts/dev-local.sh status            # 检查环境状态"
    echo "  ./scripts/dev-local.sh size              # 显示文件大小"
    echo ""
    echo -e "${YELLOW}💡 提示:${NC}"
    echo "  - 本地开发适合快速测试和调试"
    echo "  - VPS开发适合长期运行和团队协作"
    echo "  - 使用 ./scripts/dev-ssh.sh 进行VPS开发"
}

# 检查环境
check_env() {
    echo -e "${BLUE}🔍 检查开发环境...${NC}"
    
    # 检查 Go
    if command -v go &> /dev/null; then
        echo -e "${GREEN}✅ Go: $(go version)${NC}"
    else
        echo -e "${RED}❌ Go 未安装${NC}"
        return 1
    fi
    
    # 检查 Node.js
    if command -v node &> /dev/null; then
        echo -e "${GREEN}✅ Node.js: $(node --version)${NC}"
    else
        echo -e "${RED}❌ Node.js 未安装${NC}"
        return 1
    fi
    
    # 检查 npm
    if command -v npm &> /dev/null; then
        echo -e "${GREEN}✅ npm: $(npm --version)${NC}"
    else
        echo -e "${RED}❌ npm 未安装${NC}"
        return 1
    fi
    
    # 检查 Air
    if [ -f "./tools/air" ]; then
        echo -e "${GREEN}✅ Air: 项目本地版本${NC}"
    elif command -v air &> /dev/null; then
        echo -e "${GREEN}✅ Air: 系统版本${NC}"
    else
        echo -e "${YELLOW}⚠️  Air 未安装，将自动安装${NC}"
    fi
    
    return 0
}

# 安装依赖
install_deps() {
    echo -e "${GREEN}📦 安装项目依赖...${NC}"
    make install
}

# 安装 Air
install_air() {
    echo -e "${GREEN}📦 安装 Air...${NC}"
    make install-air
}

# 启动开发环境
start_dev() {
    echo -e "${GREEN}🚀 启动本地开发环境...${NC}"
    if ! check_env; then
        echo -e "${RED}❌ 环境检查失败，请先安装必要的工具${NC}"
        return 1
    fi
    make dev
}

# 构建项目
build_project() {
    echo -e "${GREEN}🔨 构建项目...${NC}"
    make release-build
}

# 运行测试
run_tests() {
    echo -e "${GREEN}🧪 运行测试...${NC}"
    make test
}

# 清理文件
clean_files() {
    echo -e "${GREEN}🧹 清理构建文件...${NC}"
    make clean
}

# 显示文件大小
show_size() {
    echo -e "${GREEN}📊 显示文件大小...${NC}"
    make size
}

# 检查服务状态
check_status() {
    echo -e "${BLUE}📊 检查本地服务状态...${NC}"
    
    # 检查端口 9090
    if lsof -i :9090 &> /dev/null; then
        echo -e "${GREEN}✅ 服务正在运行 (端口 9090)${NC}"
        echo -e "${GREEN}🌐 访问地址: http://localhost:9090${NC}"
        echo ""
        echo -e "${BLUE}进程信息:${NC}"
        lsof -i :9090
    else
        echo -e "${RED}❌ 服务未运行 (端口 9090)${NC}"
    fi
    
    echo ""
    check_env
}

# 主逻辑
main() {
    case "${1:-help}" in
        "help"|"-h"|"--help")
            show_help
            ;;
        "dev")
            start_dev
            ;;
        "build")
            build_project
            ;;
        "test")
            run_tests
            ;;
        "install")
            install_deps
            ;;
        "install-air")
            install_air
            ;;
        "clean")
            clean_files
            ;;
        "size")
            show_size
            ;;
        "status")
            check_status
            ;;
        *)
            echo -e "${RED}❌ 未知命令: $1${NC}"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

main "$@"
